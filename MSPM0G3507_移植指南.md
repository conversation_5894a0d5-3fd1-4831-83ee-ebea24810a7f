# A4950电机驱动库移植到MSPM0G3507指南

## 项目概述
本指南将帮助您将A4950电机驱动库从STM32平台移植到MSPM0G3507平台，并在CCS开发环境中配置新的引脚定义。

## 硬件引脚映射

| 功能 | MSPM0G3507引脚 | 物理引脚 | 功能描述 |
|------|----------------|----------|----------|
| 左电机PWM | PA12 | Pin 34 | TIMG0_CCP0 - 左电机速度控制 |
| 右电机PWM | PA13 | Pin 35 | TIMG0_CCP1 - 右电机速度控制 |
| 左电机DIR | PB6 | Pin 58 | AIN1 - 左电机方向控制 |
| 右电机DIR | PB25 | Pin 27 | BIN1 - 右电机方向控制 |

## 移植步骤

### 第一步：创建CCS工程并配置引脚

1. **创建新的CCS工程**
   - 打开CCS，选择 File → New → CCS Project
   - 选择MSPM0G3507作为目标器件
   - 选择Empty Project模板

2. **使用SysConfig配置引脚**
   - 在工程中打开 `.syscfg` 文件
   - 配置以下模块：

   **TIMG0配置（PWM）：**
   ```
   - 添加TIMG0模块
   - 启用CCP0和CCP1通道
   - 设置PWM频率为5kHz
   - CCP0输出引脚：PA12 (Pin 34)
   - CCP1输出引脚：PA13 (Pin 35)
   ```

   **GPIO配置（方向控制）：**
   ```
   - 配置PB6 (Pin 58) 为输出模式
   - 配置PB25 (Pin 27) 为输出模式
   - 初始状态设为低电平
   ```

### 第二步：修改头文件 (motor_driver.h)

创建适配MSPM0G3507的头文件：

```c
/**
 * @file    motor_driver.h
 * @brief   A4950电机驱动库 - MSPM0G3507版本
 */

#ifndef __MOTOR_DRIVER_H__
#define __MOTOR_DRIVER_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "ti_msp_dl_config.h"

/* Exported types ------------------------------------------------------------*/
typedef enum {
    MOTOR_STATE_STOP = 0,     // 停止
    MOTOR_STATE_FORWARD,      // 正转
    MOTOR_STATE_BACKWARD,     // 反转
    MOTOR_STATE_ERROR         // 错误
} MotorState_t;

typedef struct {
    GPTIMER_Regs* timer_inst;       // 定时器实例
    DL_TIMER_CC_INDEX cc_index;     // 比较通道索引
    GPIO_Regs* dir_port;            // 方向控制GPIO端口
    uint32_t dir_pin;               // 方向控制GPIO引脚
} MotorHW_t;

typedef struct {
    MotorHW_t hw;                   // 硬件配置
    int8_t speed;                   // 当前速度 (-100 到 +100)
    MotorState_t state;             // 当前状态
    uint8_t enable;                 // 使能标志
    uint8_t reverse;                // 电机安装方向
} Motor_t;

/* Exported constants --------------------------------------------------------*/
#define MOTOR_SPEED_MAX         100      // 最大速度
#define MOTOR_SPEED_MIN         -100     // 最小速度
#define MOTOR_PWM_PERIOD        1000     // PWM周期值
#define MOTOR_MIN_PWM_THRESHOLD 100      // 最小PWM阈值

/* Exported functions --------------------------------------------------------*/
int8_t Motor_Create(Motor_t* motor, 
                    GPTIMER_Regs* timer_inst,
                    DL_TIMER_CC_INDEX cc_index,
                    GPIO_Regs* dir_port, 
                    uint32_t dir_pin, 
                    uint8_t reverse);

int8_t Motor_SetSpeed(Motor_t* motor, int8_t speed);
int8_t Motor_Stop(Motor_t* motor);
MotorState_t Motor_GetState(Motor_t* motor);
int8_t Motor_Enable(Motor_t* motor, uint8_t enable);
void Motor_brake(Motor_t *motor);

#ifdef __cplusplus
}
#endif

#endif /* __MOTOR_DRIVER_H__ */
```

### 第三步：修改源文件 (motor_driver.c)

关键修改点：

1. **包含文件替换**：
   ```c
   #include "motor_driver.h"
   // 移除STM32相关包含
   ```

2. **PWM控制函数替换**：
   ```c
   // STM32版本：
   __HAL_TIM_SET_COMPARE(motor->hw.htim, motor->hw.channel, pwm_value);
   
   // MSPM0版本：
   DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, pwm_value, motor->hw.cc_index);
   ```

3. **GPIO控制函数替换**：
   ```c
   // STM32版本：
   HAL_GPIO_WritePin(motor->hw.dir_port, motor->hw.dir_pin, GPIO_PIN_SET);
   
   // MSPM0版本：
   DL_GPIO_setPins(motor->hw.dir_port, motor->hw.dir_pin);
   DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
   ```

### 第四步：修改应用层文件 (motor_app.c)

```c
#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
    // 右电机：PA13 (TIMG0_CCP1) + PB25 (DIR)
    Motor_Create(&right_motor, 
                 TIMG0,                    // 定时器实例
                 DL_TIMER_CC_1_INDEX,      // CCP1通道
                 GPIOB,                    // PB端口
                 DL_GPIO_PIN_25,           // PB25引脚
                 1);                       // 反装
    
    // 左电机：PA12 (TIMG0_CCP0) + PB6 (DIR)  
    Motor_Create(&left_motor,
                 TIMG0,                    // 定时器实例
                 DL_TIMER_CC_0_INDEX,      // CCP0通道
                 GPIOB,                    // PB端口
                 DL_GPIO_PIN_6,            // PB6引脚
                 0);                       // 正装
}
```

### 第五步：在main.c中集成

```c
#include "ti_msp_dl_config.h"
#include "motor_app.h"

int main(void)
{
    SYSCFG_DL_init();
    
    // 初始化电机
    Motor_Init();
    
    while (1) {
        // 示例：双电机前进
        Motor_SetSpeed(&left_motor, 50);   // 左电机50%速度
        Motor_SetSpeed(&right_motor, 50);  // 右电机50%速度
        delay_cycles(16000000);            // 延时约1秒@16MHz
        
        // 停止
        Motor_Stop(&left_motor);
        Motor_Stop(&right_motor);
        delay_cycles(16000000);            // 延时约1秒
    }
}
```

## 编译配置

1. **添加源文件到工程**：
   - 将 `motor_driver.c` 和 `motor_app.c` 添加到CCS工程
   - 确保头文件路径正确

2. **编译器设置**：
   - 确保使用TI ARM编译器
   - 添加必要的包含路径

## 测试验证

1. **硬件连接测试**：
   - 使用万用表验证引脚输出
   - 检查PWM波形是否正确

2. **功能测试**：
   - 测试电机正转、反转、停止
   - 验证速度控制精度
   - 测试方向控制逻辑

## 注意事项

1. **时钟配置**：确保TIMG0时钟源配置正确
2. **引脚复用**：检查引脚是否与其他功能冲突
3. **电源管理**：MSPM0功耗较低，注意电机驱动电流需求
4. **调试工具**：使用CCS内置调试器进行代码调试

## 完整的motor_driver.c实现

```c
#include "motor_driver.h"

/* Private function prototypes */
static uint32_t Speed_To_PWM(int8_t speed);
static int8_t Motor_ValidateParams(Motor_t *motor);

/* Exported functions */
int8_t Motor_Create(Motor_t *motor,
                    GPTIMER_Regs *timer_inst,
                    DL_TIMER_CC_INDEX cc_index,
                    GPIO_Regs *dir_port,
                    uint32_t dir_pin,
                    uint8_t reverse)
{
    if (motor == NULL || timer_inst == NULL || dir_port == NULL) {
        return -1;
    }

    // 初始化硬件配置
    motor->hw.timer_inst = timer_inst;
    motor->hw.cc_index = cc_index;
    motor->hw.dir_port = dir_port;
    motor->hw.dir_pin = dir_pin;

    // 初始化电机状态
    motor->speed = 0;
    motor->state = MOTOR_STATE_STOP;
    motor->enable = 1;
    motor->reverse = reverse;

    // 设置初始状态：停止
    DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index);

    return 0;
}

int8_t Motor_SetSpeed(Motor_t *motor, int8_t speed)
{
    if (Motor_ValidateParams(motor) != 0) return -1;
    if (speed < MOTOR_SPEED_MIN || speed > MOTOR_SPEED_MAX) return -1;
    if (!motor->enable) return -1;

    motor->speed = speed;

    if (speed == 0) {
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
        DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index);
        motor->state = MOTOR_STATE_STOP;
        return 0;
    }

    int8_t actual_speed = motor->reverse ? -speed : speed;
    uint32_t pwm_value;

    if (actual_speed < 0) {
        DL_GPIO_setPins(motor->hw.dir_port, motor->hw.dir_pin);
        pwm_value = Speed_To_PWM(100 + actual_speed);
        motor->state = MOTOR_STATE_BACKWARD;
    } else {
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
        pwm_value = Speed_To_PWM(actual_speed);
        motor->state = MOTOR_STATE_FORWARD;
    }

    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, pwm_value, motor->hw.cc_index);
    return 0;
}

int8_t Motor_Stop(Motor_t *motor)
{
    if (Motor_ValidateParams(motor) != 0) return -1;

    DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index);

    motor->speed = 0;
    motor->state = MOTOR_STATE_STOP;
    return 0;
}

void Motor_brake(Motor_t *motor)
{
    DL_GPIO_setPins(motor->hw.dir_port, motor->hw.dir_pin);
    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, MOTOR_PWM_PERIOD, motor->hw.cc_index);
}

MotorState_t Motor_GetState(Motor_t *motor)
{
    if (Motor_ValidateParams(motor) != 0) return MOTOR_STATE_ERROR;
    return motor->state;
}

int8_t Motor_Enable(Motor_t *motor, uint8_t enable)
{
    if (Motor_ValidateParams(motor) != 0) return -1;

    motor->enable = enable;
    if (!enable) {
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
        DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index);
        motor->speed = 0;
        motor->state = MOTOR_STATE_STOP;
    }
    return 0;
}

/* Private functions */
static uint32_t Speed_To_PWM(int8_t speed)
{
    uint8_t abs_speed = (speed < 0) ? (uint8_t)(-speed) : (uint8_t)speed;
    if (abs_speed == 0) return 0;

    uint32_t pwm_value = (uint32_t)abs_speed * MOTOR_PWM_PERIOD / 100;

    if (pwm_value > 0 && pwm_value < MOTOR_MIN_PWM_THRESHOLD) {
        pwm_value = MOTOR_MIN_PWM_THRESHOLD;
    }

    if (pwm_value > MOTOR_PWM_PERIOD) {
        pwm_value = MOTOR_PWM_PERIOD;
    }

    return pwm_value;
}

static int8_t Motor_ValidateParams(Motor_t *motor)
{
    if (motor == NULL || motor->hw.timer_inst == NULL || motor->hw.dir_port == NULL) {
        return -1;
    }
    return 0;
}
```

## SysConfig详细配置步骤

### TIMG0 PWM配置
1. 在SysConfig中添加TIMG0模块
2. 配置参数：
   ```
   - Clock Source: BUSCLK (32MHz)
   - Clock Divider: 32 (得到1MHz时钟)
   - Period: 200 (5kHz PWM频率)
   - CCP0 Enable: ✓
   - CCP1 Enable: ✓
   - CCP0 Pin: PA12
   - CCP1 Pin: PA13
   ```

### GPIO配置
1. 配置PB6和PB25为输出：
   ```
   - Direction: Output
   - Initial Value: Low
   - Drive Strength: Standard
   ```

## 故障排除

- **PWM无输出**：检查SysConfig中TIMG0配置和引脚映射
- **方向控制失效**：验证GPIO配置和引脚连接
- **编译错误**：检查头文件包含路径和API函数名称
- **电机不转**：检查A4950电源和使能信号

## 性能优化建议

1. **中断驱动**：可考虑使用定时器中断实现平滑加减速
2. **PID控制**：结合编码器反馈实现闭环速度控制
3. **功耗优化**：利用MSPM0的低功耗特性，在停止时进入睡眠模式

移植完成后，您将拥有一个完全适配MSPM0G3507的高效A4950电机驱动库，支持CCS开发环境和新的引脚配置。
